#!/bin/bash

# Auto-discover databases and generate PostgREST instances
# Usage: ./auto-discover.sh

# Configuration
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_USER="username"
POSTGRES_PASSWORD="password"
BASE_PORT=3000

# Exclude system databases
EXCLUDE_DBS="template0|template1|postgres"

echo "🔍 Discovering databases..."

# Get list of databases from PostgreSQL
DATABASES=$(PGPASSWORD=$POSTGRES_PASSWORD psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -t -c "SELECT datname FROM pg_database WHERE datistemplate = false;" | grep -v -E "$EXCLUDE_DBS" | sed 's/^ *//' | sed 's/ *$//')

if [ -z "$DATABASES" ]; then
    echo "❌ No databases found or connection failed"
    echo "Please check your PostgreSQL connection settings"
    exit 1
fi

# Convert to array
DB_ARRAY=()
while IFS= read -r line; do
    if [ ! -z "$line" ]; then
        DB_ARRAY+=("$line")
    fi
done <<< "$DATABASES"

echo "📊 Found ${#DB_ARRAY[@]} databases:"
for db in "${DB_ARRAY[@]}"; do
    echo "  - $db"
done
echo ""

# Generate docker-compose header
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
EOF

# Generate PostgREST service for each database
for i in "${!DB_ARRAY[@]}"; do
    DB_NAME="${DB_ARRAY[$i]}"
    SERVICE_NAME="postgrest-${DB_NAME//_/-}"
    CONTAINER_NAME="postgrest-${DB_NAME//_/-}"
    PORT=$((BASE_PORT + i))
    JWT_SECRET="${DB_NAME}-jwt-secret-key-32-characters-minimum"
    
    cat >> docker-compose.yml << EOF

  # PostgREST for ${DB_NAME}
  ${SERVICE_NAME}:
    image: postgrest/postgrest:v12.2.12
    container_name: ${CONTAINER_NAME}
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${DB_NAME}
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:${PORT}
    ports:
      - "${PORT}:3000"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
EOF
done

# Add networks section
cat >> docker-compose.yml << 'EOF'

networks:
  default:
    name: postgrest_network
    driver: bridge
EOF

echo "✅ Generated docker-compose.yml with ${#DB_ARRAY[@]} PostgREST instances"
echo ""
echo "📋 API Endpoints:"
for i in "${!DB_ARRAY[@]}"; do
    DB_NAME="${DB_ARRAY[$i]}"
    PORT=$((BASE_PORT + i))
    echo "  ${DB_NAME}: http://localhost:${PORT}"
done
echo ""
echo "🚀 To start: docker-compose up -d"
echo "📖 OpenAPI docs: http://localhost:PORT/ (for each service)"
