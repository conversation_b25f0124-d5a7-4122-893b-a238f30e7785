version: '3.8'

services:
  # PostgREST API Server
  postgrest:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-api
    restart: unless-stopped
    environment:
      # Database connection
      PGRST_DB_URI: **************************************/database_name
      
      # Schema configuration
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      
      # JWT configuration
      PGRST_JWT_SECRET: your-jwt-secret-key-32-characters-minimum
      
      # Additional settings
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: your-jwt-secret-key-32-characters-minimum
      PGRST_APP_SETTINGS_JWT_EXP: 3600
      
      # Optional: Enable OpenAPI documentation
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:3000
      
    ports:
      - "3000:3000"
    
    # Health check
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  default:
    name: postgrest_network
    driver: bridge
