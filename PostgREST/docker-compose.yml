version: '3.8'

services:
  # PostgREST for Pharmacy Database
  postgrest-pharmacy:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-pharmacy
    restart: unless-stopped
    environment:
      PGRST_DB_URI: **************************************/pharmacy_management
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: pharmacy-jwt-secret-key-32-characters-minimum
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:3000
    ports:
      - "3000:3000"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # PostgREST for Inventory Database
  postgrest-inventory:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-inventory
    restart: unless-stopped
    environment:
      PGRST_DB_URI: **************************************/inventory_management
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: inventory-jwt-secret-key-32-characters-minimum
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:3001
    ports:
      - "3001:3000"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # PostgREST for Sales Database
  postgrest-sales:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-sales
    restart: unless-stopped
    environment:
      PGRST_DB_URI: **************************************/sales_management
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: sales-jwt-secret-key-32-characters-minimum
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:3002
    ports:
      - "3002:3000"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Add more PostgREST instances as needed...

networks:
  default:
    name: postgrest_network
    driver: bridge
