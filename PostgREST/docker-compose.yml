version: '3.8'
services:

  postgrest-n8n-db:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-n8n-db
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://haint:H%40iDu0ng@192.168.1.221:5432/n8n_db
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon_user
      PGRST_JWT_SECRET: n8n_db-jwt-secret
    ports:
      - "3000:3000"

  postgrest-postiz:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-postiz
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://haint:H%40iDu0ng@192.168.1.221:5432/postiz
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: anon_user
      PGRST_JWT_SECRET: postiz-jwt-secret
    ports:
      - "3001:3000"
