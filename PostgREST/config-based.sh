#!/bin/bash

# Generate PostgREST from configuration file
# Usage: ./config-based.sh

# Configuration
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_USER="username"
POSTGRES_PASSWORD="password"
BASE_PORT=3000
CONFIG_FILE="databases.conf"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Configuration file $CONFIG_FILE not found"
    exit 1
fi

echo "📖 Reading configuration from $CONFIG_FILE..."

# Generate docker-compose header
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
EOF

# Read configuration file and generate services
while IFS=':' read -r db_name schema port_offset description; do
    # Skip comments and empty lines
    if [[ $db_name =~ ^#.*$ ]] || [[ -z "$db_name" ]]; then
        continue
    fi
    
    SERVICE_NAME="postgrest-${db_name//_/-}"
    CONTAINER_NAME="postgrest-${db_name//_/-}"
    PORT=$((BASE_PORT + port_offset))
    JWT_SECRET="${db_name}-jwt-secret-key-32-characters-minimum"
    
    echo "  Adding: $db_name (port $PORT) - $description"
    
    cat >> docker-compose.yml << EOF

  # PostgREST for ${db_name} - ${description}
  ${SERVICE_NAME}:
    image: postgrest/postgrest:v12.2.12
    container_name: ${CONTAINER_NAME}
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${db_name}
      PGRST_DB_SCHEMAS: ${schema}
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:${PORT}
    ports:
      - "${PORT}:3000"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
EOF

done < "$CONFIG_FILE"

# Add networks section
cat >> docker-compose.yml << 'EOF'

networks:
  default:
    name: postgrest_network
    driver: bridge
EOF

echo ""
echo "✅ Generated docker-compose.yml from configuration"
echo ""
echo "📋 API Endpoints:"
while IFS=':' read -r db_name schema port_offset description; do
    if [[ $db_name =~ ^#.*$ ]] || [[ -z "$db_name" ]]; then
        continue
    fi
    PORT=$((BASE_PORT + port_offset))
    echo "  ${db_name}: http://localhost:${PORT} - ${description}"
done < "$CONFIG_FILE"
echo ""
echo "🚀 To start: docker-compose up -d"
