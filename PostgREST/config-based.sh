#!/bin/bash

# Simple PostgREST generator
# Usage: ./config-based.sh

# Configuration - UPDATE THESE VALUES
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_USER="haint"
POSTGRES_PASSWORD="H@iDu0ng"
BASE_PORT=3000

echo "🚀 Generating PostgREST for multiple databases..."

# Generate docker-compose header
cat > docker-compose.yml << 'EOF'
version: '3.8'
services:
EOF

# Read databases from config file
port_counter=0
while read -r db_name; do
    # Skip comments and empty lines
    [[ $db_name =~ ^#.*$ ]] || [[ -z "$db_name" ]] && continue

    PORT=$((BASE_PORT + port_counter))

    echo "  Adding: $db_name (port $PORT)"

    cat >> docker-compose.yml << EOF

  postgrest-${db_name//_/-}:
    image: postgrest/postgrest:v12.2.12
    container_name: postgrest-${db_name//_/-}
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${db_name}
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: ${db_name}-jwt-secret
    ports:
      - "${PORT}:3000"
EOF

    ((port_counter++))
done < databases.conf

echo ""
echo "✅ Done! API endpoints:"
port_counter=0
while read -r db_name; do
    [[ $db_name =~ ^#.*$ ]] || [[ -z "$db_name" ]] && continue
    PORT=$((BASE_PORT + port_counter))
    echo "  ${db_name}: http://localhost:${PORT}"
    ((port_counter++))
done < databases.conf
echo ""
echo "🚀 Run: docker-compose up -d"
