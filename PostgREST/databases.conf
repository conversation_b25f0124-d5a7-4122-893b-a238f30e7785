# Database Configuration File
# Format: database_name:schema:port_offset:description

# Core databases
pharmacy_management:public:0:Main pharmacy management system
inventory_management:public:1:Inventory tracking system
sales_management:public:2:Sales and transactions
user_management:public:3:User authentication and roles

# Analytics databases  
reporting_db:public:4:Business intelligence and reports
analytics_db:public:5:Data analytics and metrics

# Development databases
pharmacy_test:public:10:Test environment for pharmacy
staging_db:public:11:Staging environment

# Add more databases as needed...
