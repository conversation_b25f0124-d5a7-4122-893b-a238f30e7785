-- =============================================================================
-- PostgREST Permissions Setup Script
-- =============================================================================

-- Tạo schema (nếu bạn không dùng 'public')
-- CREATE SCHEMA IF NOT EXISTS api;

-- Tạo vai trò ẩn danh mà PostgREST sẽ sử dụng
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'anon_user') THEN
        CREATE ROLE anon_user NOLOGIN;
    END IF;
END
$$;
GRANT USAGE ON SCHEMA public TO anon_user;

-- Tạo một vai trò để PostgREST kết nối với tư cách là (role này có trong PGRST_DB_URI)
-- User 'haint' đã được tạo sẵn
-- Chúng ta chỉ cần cấp quyền cho nó để nó có thể SET ROLE sang anon_user
GRANT anon_user TO haint;

-- Cấp quyền cơ bản cho vai trò ẩn danh
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anon_user;

-- Cấp quyền cho các bảng được tạo trong tương lai
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO anon_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE ON SEQUENCES TO anon_user;

-- (Tùy chọn) Nếu muốn cho phép insert/update/delete (cẩn thận với vai trò ẩn danh!)
-- GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO web_anon;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT INSERT, UPDATE, DELETE ON TABLES TO web_anon;

-- (Ví dụ) Tạo một bảng test
CREATE TABLE IF NOT EXISTS public.api_test (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cấp quyền cho vai trò ẩn danh trên bảng test
GRANT SELECT ON TABLE public.api_test TO anon_user;

-- (Ví dụ) Thêm dữ liệu mẫu
INSERT INTO public.api_test (name, description) VALUES
  ('PostgREST Test', 'API endpoint test'),
  ('Database Connection', 'Kiểm tra kết nối database'),
  ('Permissions Check', 'Kiểm tra quyền truy cập')
ON CONFLICT DO NOTHING;

-- Thông báo hoàn thành
SELECT 'PostgREST permissions setup completed successfully!' as status;
