#!/bin/bash

# Setup PostgREST permissions for all databases
# Usage: ./setup-all-permissions.sh

# Configuration
POSTGRES_HOST="*************"
POSTGRES_PORT="5432"
POSTGRES_USER="haint"
POSTGRES_PASSWORD="H@iDu0ng"

echo "🔧 Setting up PostgREST permissions for all databases..."

# Read databases from config file
while read -r db_name; do
    # Skip comments and empty lines
    [[ $db_name =~ ^#.*$ ]] || [[ -z "$db_name" ]] && continue
    
    echo "  📋 Setting up permissions for: $db_name"
    
    # Run setup script for each database
    PGPASSWORD=$POSTGRES_PASSWORD psql \
        -h $POSTGRES_HOST \
        -p $POSTGRES_PORT \
        -U $POSTGRES_USER \
        -d $db_name \
        -f setup-permissions.sql \
        -q
    
    if [ $? -eq 0 ]; then
        echo "  ✅ $db_name: Permissions setup completed"
    else
        echo "  ❌ $db_name: Failed to setup permissions"
    fi
    
done < databases.conf

echo ""
echo "🚀 All databases setup completed!"
echo "📋 You can now start PostgREST:"
echo "   docker-compose up -d"
echo ""
echo "🔗 API Endpoints will be available at:"
port_counter=0
while read -r db_name; do
    [[ $db_name =~ ^#.*$ ]] || [[ -z "$db_name" ]] && continue
    PORT=$((3000 + port_counter))
    echo "   ${db_name}: http://localhost:${PORT}"
    ((port_counter++))
done < databases.conf
