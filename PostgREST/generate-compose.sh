#!/bin/bash

# Auto-generate docker-compose.yml for multiple databases
# Usage: ./generate-compose.sh

# Configuration
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_USER="username"
POSTGRES_PASSWORD="password"
BASE_PORT=3000

# Database list - Add your databases here
DATABASES=(
    "pharmacy_management"
    "inventory_management" 
    "sales_management"
    "user_management"
    "reporting_db"
)

# Generate docker-compose header
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
EOF

# Generate PostgREST service for each database
for i in "${!DATABASES[@]}"; do
    DB_NAME="${DATABASES[$i]}"
    SERVICE_NAME="postgrest-${DB_NAME//_/-}"
    CONTAINER_NAME="postgrest-${DB_NAME//_/-}"
    PORT=$((BASE_PORT + i))
    JWT_SECRET="${DB_NAME}-jwt-secret-key-32-characters-minimum"
    
    cat >> docker-compose.yml << EOF

  # PostgREST for ${DB_NAME}
  ${SERVICE_NAME}:
    image: postgrest/postgrest:v12.2.12
    container_name: ${CONTAINER_NAME}
    restart: unless-stopped
    environment:
      PGRST_DB_URI: postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${DB_NAME}
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: ${JWT_SECRET}
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_OPENAPI_SERVER_PROXY_URI: http://localhost:${PORT}
    ports:
      - "${PORT}:3000"
    healthcheck:
      test: ["CMD-SHELL", "wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
EOF
done

# Add networks section
cat >> docker-compose.yml << 'EOF'

networks:
  default:
    name: postgrest_network
    driver: bridge
EOF

echo "✅ Generated docker-compose.yml with ${#DATABASES[@]} PostgREST instances"
echo ""
echo "📋 API Endpoints:"
for i in "${!DATABASES[@]}"; do
    DB_NAME="${DATABASES[$i]}"
    PORT=$((BASE_PORT + i))
    echo "  ${DB_NAME}: http://localhost:${PORT}"
done
echo ""
echo "🚀 To start: docker-compose up -d"
echo "📖 OpenAPI docs: http://localhost:PORT/ (for each service)"
