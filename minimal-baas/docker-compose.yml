version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: pharmacy-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: pharmacy_management
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: pharmacy_secure_password_2024
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d pharmacy_management"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - pharmacy_network

  # PostgREST API
  postgrest:
    image: postgrest/postgrest:v12.2.12
    container_name: pharmacy-api
    restart: unless-stopped
    environment:
      PGRST_DB_URI: ***************************************************************/pharmacy_management
      PGRST_DB_SCHEMAS: public
      PGRST_DB_ANON_ROLE: web_anon
      PGRST_JWT_SECRET: pharmacy-jwt-secret-key-32-characters-minimum-length
      PGRST_DB_USE_LEGACY_GUCS: "false"
      PGRST_APP_SETTINGS_JWT_SECRET: pharmacy-jwt-secret-key-32-characters-minimum-length
      PGRST_APP_SETTINGS_JWT_EXP: 3600
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - pharmacy_network

  # pgAdmin (Optional - Web Database Management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pharmacy-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: pharmacy_admin_2024
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - pharmacy_network
    profiles:
      - admin

  # Redis (Optional - Caching & Sessions)
  redis:
    image: redis:7-alpine
    container_name: pharmacy-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass pharmacy_redis_2024
    networks:
      - pharmacy_network
    profiles:
      - cache

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  pharmacy_network:
    driver: bridge
