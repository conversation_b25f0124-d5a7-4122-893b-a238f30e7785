-- =============================================================================
-- MINIMAL BAAS SETUP FOR PHARMACY MANAGEMENT
-- =============================================================================

-- Create roles for PostgREST
CREATE ROLE web_anon NOLOGIN;
CREATE ROLE authenticator NOINHERIT LOGIN PASSWORD 'pharmacy_auth_2024';
GRANT web_anon TO authenticator;

-- Create authenticated role
CREATE ROLE authenticated NOLOGIN;
GRANT authenticated TO authenticator;

-- Grant basic permissions
GRANT USAGE ON SCHEMA public TO web_anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Set default privileges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO authenticated;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO authenticated;

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create basic auth schema (simplified)
CREATE SCHEMA IF NOT EXISTS auth;

-- Simple users table for authentication
CREATE TABLE IF NOT EXISTS auth.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Simple sessions table
CREATE TABLE IF NOT EXISTS auth.sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT UNIQUE NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grant permissions on auth schema
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA auth TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA auth TO authenticated;

-- Helper functions
CREATE OR REPLACE FUNCTION auth.check_password(email TEXT, password TEXT)
RETURNS UUID AS $$
DECLARE
    user_id UUID;
BEGIN
    SELECT id INTO user_id 
    FROM auth.users 
    WHERE users.email = check_password.email 
    AND password_hash = crypt(password, password_hash);
    
    RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION auth.create_user(email TEXT, password TEXT, user_role TEXT DEFAULT 'user')
RETURNS UUID AS $$
DECLARE
    user_id UUID;
BEGIN
    INSERT INTO auth.users (email, password_hash, role)
    VALUES (email, crypt(password, gen_salt('bf')), user_role)
    RETURNING id INTO user_id;
    
    RETURN user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create default admin user
SELECT auth.create_user('<EMAIL>', 'pharmacy_admin_2024', 'admin');

-- Basic RLS setup
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view own data" ON auth.users
    FOR SELECT USING (id = current_setting('request.jwt.claim.sub', true)::UUID);

CREATE POLICY "Users can update own data" ON auth.users
    FOR UPDATE USING (id = current_setting('request.jwt.claim.sub', true)::UUID);

-- Notify PostgREST of schema changes
NOTIFY pgrst, 'reload schema';
